import mongoose from 'mongoose';

const ReportSchema = new mongoose.Schema({
  filename: {
    type: String,
    required: [true, 'Please provide a filename'],
    trim: true,
  },
  fileType: {
    type: String,
    required: [true, 'Please provide a file type'],
    enum: ['pdf', 'docx', 'txt'],
  },
  fileUrl: {
    type: String,
    required: [true, 'Please provide a file URL'],
  },
  userEmail: {
    type: String,
    required: [true, 'Please provide a user email'],
  },
  uploadedAt: {
    type: Date,
    default: Date.now,
  },
});

export default mongoose.models.Report || mongoose.model('Report', ReportSchema);