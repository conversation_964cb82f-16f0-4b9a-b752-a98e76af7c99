'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function Upload() {
  const [file, setFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const router = useRouter();
  
  useEffect(() => {
    // Check if user is logged in
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    
    if (!isLoggedIn) {
      router.push('/login');
    }
  }, [router]);

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (!selectedFile) return;

    // Check file type
    const fileType = selectedFile.name.split('.').pop().toLowerCase();
    if (!['pdf', 'docx', 'txt'].includes(fileType)) {
      setError('Invalid file type. Only PDF, DOCX, and TXT files are allowed.');
      setFile(null);
      return;
    }

    // Check file size (max 10MB)
    if (selectedFile.size > 10 * 1024 * 1024) {
      setError('File size exceeds 10MB limit.');
      setFile(null);
      return;
    }

    setFile(selectedFile);
    setError('');
  };

  const handleRemoveFile = () => {
    setFile(null);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!file) {
      setError('Please select a file to upload.');
      return;
    }

    setIsUploading(true);
    setError('');
    setSuccess('');

    try {
      const formData = new FormData();
      formData.append('file', file);
      
      // Add user email to form data
      const userEmail = localStorage.getItem('userEmail') || '<EMAIL>';
      formData.append('userEmail', userEmail);

      const response = await fetch('/api/reports/upload', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to upload file');
      }

      setSuccess('File uploaded successfully!');
      setFile(null);
      
      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);
    } catch (error) {
      setError(error.message || 'An error occurred while uploading the file.');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="container">
      <div className="card">
        <h1>Upload Lab Report</h1>
        <p>Upload your lab report in PDF, DOCX, or TXT format.</p>

        <form onSubmit={handleSubmit} className="upload-form">
          <div className="form-group">
            <div className="file-input-wrapper">
              <input
                type="file"
                id="file"
                className="file-input"
                onChange={handleFileChange}
                accept=".pdf,.docx,.txt"
                disabled={isUploading}
              />
              <div className="file-input-icon">📄</div>
              <div className="file-input-text">Drag & Drop or Click to Upload</div>
              <div className="file-input-subtext">
                Supported formats: PDF, DOCX, TXT (Max 10MB)
              </div>
            </div>
          </div>

          {file && (
            <div className="selected-file">
              <div className="selected-file-name">{file.name}</div>
              <div className="remove-file" onClick={handleRemoveFile}>
                ✕
              </div>
            </div>
          )}

          {error && <div className="error">{error}</div>}
          {success && <div className="success">{success}</div>}

          <div style={{ marginTop: '2rem', display: 'flex', justifyContent: 'space-between' }}>
            <Link href="/dashboard">
              <button type="button" style={{ backgroundColor: '#718096' }}>
                Cancel
              </button>
            </Link>
            <button
              type="submit"
              disabled={!file || isUploading}
            >
              {isUploading ? 'Uploading...' : 'Upload Report'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}