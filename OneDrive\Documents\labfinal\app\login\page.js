'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function Login() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSignUp, setIsSignUp] = useState(false);
  const [error, setError] = useState('');

  const handleLogin = (e) => {
    e.preventDefault();
    
    // Simple validation
    if (!email || !password) {
      setError('Please enter both email and password');
      return;
    }
    
    // In a real app, you would validate credentials against a database
    // For now, we'll just simulate a successful login
    localStorage.setItem('userEmail', email);
    localStorage.setItem('isLoggedIn', 'true');
    
    // Redirect to dashboard
    router.push('/dashboard');
  };

  return (
    <div className="login-container">
      <div className="card login-card">
        <h1>Lab Report Uploader</h1>
        <h2>{isSignUp ? 'Sign Up' : 'Login'}</h2>
        
        {error && <div className="error">{error}</div>}
        
        <form onSubmit={handleLogin} style={{ marginTop: '1rem' }}>
          <div style={{ marginBottom: '1rem' }}>
            <label htmlFor="email" style={{ display: 'block', marginBottom: '0.5rem' }}>Email</label>
            <input 
              type="email" 
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              style={{ 
                width: '100%', 
                padding: '0.5rem',
                borderRadius: '0.25rem',
                border: '1px solid #ccc'
              }}
              required
            />
          </div>
          
          <div style={{ marginBottom: '1.5rem' }}>
            <label htmlFor="password" style={{ display: 'block', marginBottom: '0.5rem' }}>Password</label>
            <input 
              type="password" 
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              style={{ 
                width: '100%', 
                padding: '0.5rem',
                borderRadius: '0.25rem',
                border: '1px solid #ccc'
              }}
              required
            />
          </div>
          
          <button 
            type="submit"
            className="primary-button"
            style={{ width: '100%', marginBottom: '1rem' }}
          >
            {isSignUp ? 'Sign Up' : 'Login'}
          </button>
        </form>
        
        <p style={{ textAlign: 'center' }}>
          {isSignUp ? 'Already have an account?' : 'Don\'t have an account?'} 
          <button 
            onClick={() => setIsSignUp(!isSignUp)}
            style={{ 
              background: 'none', 
              border: 'none', 
              color: 'var(--primary-color)', 
              cursor: 'pointer',
              textDecoration: 'underline',
              padding: 0,
              font: 'inherit'
            }}
          >
            {isSignUp ? 'Login' : 'Sign Up'}
          </button>
        </p>
      </div>
    </div>
  );
}