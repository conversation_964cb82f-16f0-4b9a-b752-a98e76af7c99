'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export default function ReportDetails({ params }) {
  const { id } = params;
  const [report, setReport] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const router = useRouter();

  useEffect(() => {
    // Check if user is logged in
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }

    if (id) {
      fetchReport();
    }
  }, [id, router]);

  const fetchReport = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/reports/${id}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Report not found');
        }
        throw new Error('Failed to fetch report');
      }
      
      const data = await response.json();
      setReport(data.report);
    } catch (error) {
      console.error('Error fetching report:', error);
      setError(error.message || 'Failed to load report. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteReport = async () => {
    if (!confirm('Are you sure you want to delete this report?')) {
      return;
    }

    try {
      const response = await fetch('/api/reports/delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reportId: id }),
      });

      if (!response.ok) {
        throw new Error('Failed to delete report');
      }

      // Redirect to dashboard after successful deletion
      router.push('/dashboard');
    } catch (error) {
      console.error('Error deleting report:', error);
      setError('Failed to delete report. Please try again later.');
    }
  };

  const formatDate = (dateString) => {
    const options = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const getFileIcon = (fileType) => {
    switch (fileType) {
      case 'pdf':
        return '📕';
      case 'docx':
        return '📘';
      case 'txt':
        return '📄';
      default:
        return '📃';
    }
  };

  if (isLoading) {
    return (
      <div className="container">
        <div className="card">
          <h1>Loading...</h1>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container">
        <div className="card">
          <h1>Error</h1>
          <p className="error">{error}</p>
          <Link href="/dashboard">
            <button style={{ marginTop: '1rem' }}>Back to Dashboard</button>
          </Link>
        </div>
      </div>
    );
  }

  if (!report) {
    return (
      <div className="container">
        <div className="card">
          <h1>Report Not Found</h1>
          <p>The requested report could not be found.</p>
          <Link href="/dashboard">
            <button style={{ marginTop: '1rem' }}>Back to Dashboard</button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      <div style={{ marginBottom: '1rem' }}>
        <Link href="/dashboard">
          <button style={{ backgroundColor: '#718096' }}>← Back to Dashboard</button>
        </Link>
      </div>

      <div className="card">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <h1 style={{ wordBreak: 'break-word' }}>{report.filename}</h1>
          <span className={`file-type-badge ${report.fileType}`}>
            {report.fileType.toUpperCase()}
          </span>
        </div>

        <div style={{ margin: '2rem 0', padding: '2rem', backgroundColor: 'rgba(0,0,0,0.03)', borderRadius: '0.5rem', textAlign: 'center' }}>
          <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>
            {getFileIcon(report.fileType)}
          </div>
          <p>Uploaded on {formatDate(report.uploadedAt)}</p>
          <p style={{ marginTop: '0.5rem', color: '#718096' }}>
            File type: {report.fileType.toUpperCase()}
          </p>
        </div>

        <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '2rem' }}>
          <a href={report.fileUrl} target="_blank" rel="noopener noreferrer">
            <button>View File</button>
          </a>
          <button 
            onClick={handleDeleteReport}
            style={{ backgroundColor: 'var(--error-color)' }}
          >
            Delete Report
          </button>
        </div>
      </div>
    </div>
  );
}