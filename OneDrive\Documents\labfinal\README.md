# Lab Report Uploader

A fullstack web application built with Next.js App Router that allows users to log in with GitHub, upload lab reports (PDF, DOCX, TXT), and view them on a personalized dashboard.

## Features

- 🔐 **Authentication**: GitHub OAuth via NextAuth.js
- 📦 **MongoDB Integration**: Store report metadata in MongoDB Atlas
- 📤 **File Upload**: Upload PDF, DOCX, and TXT files
- 📊 **Dashboard UI**: View and manage your uploaded reports
- 🔁 **Protected Routes**: Middleware to restrict access to authenticated users
- 🎨 **Pure CSS Styling**: Clean and responsive design

## Tech Stack

- **Framework**: Next.js App Router
- **Styling**: Pure CSS (no Tailwind, no UI libraries)
- **Backend**: Next.js API Routes
- **Database**: MongoDB Atlas using Mongoose
- **Auth**: GitHub OAuth via NextAuth.js
- **File Upload**: formidable for handling PDFs, DOCX, TXT
- **File Storage**: Files stored in /public/uploads/

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- MongoDB Atlas account
- GitHub OAuth App credentials

### Installation

1. Clone the repository

```bash
git clone <repository-url>
cd lab-report-uploader
```

2. Install dependencies

```bash
npm install
```

3. Set up environment variables

Create a `.env.local` file in the root directory with the following variables:

```
GITHUB_ID=your_github_client_id
GITHUB_SECRET=your_github_client_secret
NEXTAUTH_SECRET=your_nextauth_secret
MONGODB_URI=your_mongodb_connection_string
NEXTAUTH_URL=http://localhost:3000
```

### Setting up GitHub OAuth

1. Go to GitHub Developer Settings > OAuth Apps > New OAuth App
2. Set the following:
   - Application name: Lab Report Uploader (or your preferred name)
   - Homepage URL: http://localhost:3000
   - Authorization callback URL: http://localhost:3000/api/auth/callback/github
3. Register the application and copy the Client ID and Client Secret to your `.env.local` file

### Setting up MongoDB Atlas

1. Create a MongoDB Atlas account if you don't have one
2. Create a new cluster
3. Create a database user with read/write permissions
4. Add your IP address to the IP Access List
5. Get your connection string and add it to your `.env.local` file

### Running the Application

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
/app
  /login/page.jsx - GitHub OAuth login page
  /upload/page.jsx - File upload form
  /dashboard/page.jsx - List of user's uploaded reports
  /dashboard/[id]/page.jsx - Individual report view
  /layout.jsx - Root layout with AuthProvider
/api
  /auth/[...nextauth]/route.js - NextAuth.js API route
  /reports
    /route.js - GET handler for fetching reports
    /upload/route.js - POST handler for uploading reports
    /delete/route.js - POST handler for deleting reports
    /[id]/route.js - GET handler for fetching a single report
/lib
  /mongodb.js - MongoDB connection
  /auth.js - NextAuth.js configuration
/models
  /Report.js - Mongoose model for reports
/public
  /uploads - Directory for uploaded files
/styles
  /global.css - Global CSS styles
/middleware.js - Middleware for protecting routes
```

## License

This project is licensed under the MIT License.
