import { NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { mkdir } from 'fs/promises';
import { addReport } from '@/lib/reportsStore';

export const dynamic = 'force-dynamic';



export async function POST(req) {
  try {

    // Ensure uploads directory exists
    const uploadsDir = path.join(process.cwd(), 'public/uploads');
    try {
      await fs.access(uploadsDir);
    } catch (error) {
      await mkdir(uploadsDir, { recursive: true });
    }

    // Parse form data
    const formData = await req.formData();
    const file = formData.get('file');

    if (!file) {
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
    }

    // Check file type
    const originalFilename = file.name;
    const fileExtension = path.extname(originalFilename).toLowerCase();
    
    if (!['.pdf', '.docx', '.txt'].includes(fileExtension)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only PDF, DOCX, and TXT files are allowed' },
        { status: 400 }
      );
    }

    // Create a unique filename
    const timestamp = Date.now();
    const filename = `${timestamp}-${originalFilename}`;
    const filepath = path.join(uploadsDir, filename);

    // Convert file to buffer and save it
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await fs.writeFile(filepath, buffer);

    // Create a report object
    const fileType = fileExtension.replace('.', '');
    const fileUrl = `/uploads/${filename}`;
    
    // Get user email from form data or use a default
    const userEmail = formData.get('userEmail') || '<EMAIL>';
    
    const newReport = {
      _id: Date.now().toString(),
      filename: originalFilename,
      fileType,
      fileUrl,
      userEmail,
      uploadedAt: new Date().toISOString()
    };
    
    // Add the report to the store
    addReport(newReport);

    return NextResponse.json({ success: true, report: newReport }, { status: 201 });
  } catch (error) {
    console.error('Error uploading file:', error);
    return NextResponse.json(
      { error: 'Error uploading file', details: error.message },
      { status: 500 }
    );
  }
}