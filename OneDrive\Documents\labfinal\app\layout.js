import { G<PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import "../styles/global.css";
import { ToastProvider } from "../components/Toast";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "Lab Report Uploader",
  description: "Upload and manage your lab reports",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable}`}>
        <ToastProvider>
          <div className="header">
            <div className="logo">
              <h1>Lab Report Uploader</h1>
            </div>
            <nav className="nav">
              <a href="/dashboard">Dashboard</a>
              <a href="/upload">Upload</a>
            </nav>
          </div>
          {children}
        </ToastProvider>
      </body>
    </html>
  );
}
