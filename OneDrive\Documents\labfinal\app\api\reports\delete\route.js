import { NextResponse } from 'next/server';
import { deleteReport } from '@/lib/reportsStore';

export const dynamic = 'force-dynamic';

export async function POST(req) {
  try {
    // Get report ID from request body
    const { reportId } = await req.json();
    if (!reportId) {
      return NextResponse.json({ error: 'Report ID is required' }, { status: 400 });
    }

    // Delete the report from the store
    deleteReport(reportId);
    console.log(`Deleted report ID: ${reportId}`);
    
    return NextResponse.json({ success: true }, { status: 200 });
  } catch (error) {
    console.error('Error deleting report:', error);
    return NextResponse.json(
      { error: 'Error deleting report', details: error.message },
      { status: 500 }
    );
  }
}