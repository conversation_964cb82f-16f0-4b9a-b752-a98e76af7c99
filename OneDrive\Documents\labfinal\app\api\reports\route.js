import { NextResponse } from 'next/server';
import { getReports } from '@/lib/reportsStore';

export const dynamic = 'force-dynamic';

// GET handler to fetch all reports
export async function GET(req) {
  try {
    const reports = getReports();
    return NextResponse.json({ reports }, { status: 200 });
  } catch (error) {
    console.error('Error fetching reports:', error);
    return NextResponse.json(
      { error: 'Error fetching reports', details: error.message },
      { status: 500 }
    );
  }
}

export const dynamic = 'force-dynamic';

// Mock data for reports
const mockReports = [
  {
    _id: '1',
    filename: 'Blood Test Report.pdf',
    fileType: 'pdf',
    fileUrl: '/uploads/blood-test-report.pdf',
    userEmail: '<EMAIL>',
    uploadedAt: new Date('2023-10-15').toISOString()
  },
  {
    _id: '2',
    filename: 'X-Ray Analysis.docx',
    fileType: 'docx',
    fileUrl: '/uploads/xray-analysis.docx',
    userEmail: '<EMAIL>',
    uploadedAt: new Date('2023-10-10').toISOString()
  },
  {
    _id: '3',
    filename: 'Medical Notes.txt',
    fileType: 'txt',
    fileUrl: '/uploads/medical-notes.txt',
    userEmail: '<EMAIL>',
    uploadedAt: new Date('2023-10-05').toISOString()
  }
];

// GET handler to fetch all reports (using mock data)
export async function GET(req) {
  try {
    // Return mock reports
    return NextResponse.json({ reports: mockReports }, { status: 200 });
  } catch (error) {
    console.error('Error fetching reports:', error);
    return NextResponse.json(
      { error: 'Error fetching reports', details: error.message },
      { status: 500 }
    );
  }
}