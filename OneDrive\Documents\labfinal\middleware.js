import { getToken } from 'next-auth/jwt';
import { NextResponse } from 'next/server';

export async function middleware(req) {
  // Authentication bypass - allow all access
  return NextResponse.next();
  
  /* Original authentication code (commented out)
  const path = req.nextUrl.pathname;

  // Define protected routes
  const protectedRoutes = [
    '/dashboard',
    '/upload',
  ];

  // Check if the path is a protected route or starts with a protected route
  const isProtectedRoute = protectedRoutes.some(route => 
    path === route || path.startsWith(`${route}/`)
  );

  if (isProtectedRoute) {
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

    // Redirect to login if not authenticated
    if (!token) {
      const url = new URL('/login', req.url);
      url.searchParams.set('callbackUrl', encodeURI(req.url));
      return NextResponse.redirect(url);
    }
  }
  */
}

export const config = {
  matcher: [
    '/dashboard/:path*',
    '/upload/:path*',
  ],
};