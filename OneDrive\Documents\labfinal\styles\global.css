:root {
  --primary-color: #4a6bff;
  --secondary-color: #38b2ac;
  --accent-color: #f6ad55;
  --background-color: #f7fafc;
  --text-color: #2d3748;
  --error-color: #e53e3e;
  --success-color: #38a169;
  --border-color: #e2e8f0;
  --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.3s ease-in-out;
}

a:hover {
  text-decoration: underline;
}

button {
  cursor: pointer;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.25rem;
  background-color: var(--primary-color);
  color: white;
  font-weight: 600;
  transition: all 0.3s ease-in-out;
}

button:hover {
  background-color: #3a56d4;
  transform: translateY(-2px);
}

button:disabled {
  background-color: var(--border-color);
  cursor: not-allowed;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.card {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: var(--card-shadow);
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

input,
textarea,
select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.25rem;
  font-size: 1rem;
  transition: all 0.3s ease-in-out;
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(74, 107, 255, 0.2);
}

.error {
  color: var(--error-color);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.success {
  color: var(--success-color);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.nav {
  display: flex;
  gap: 1.5rem;
}

.report-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.report-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.report-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.report-card-body {
  flex: 1;
}

.report-card-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.file-type-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  background-color: var(--primary-color);
  color: white;
}

.file-type-badge.pdf {
  background-color: var(--error-color);
}

.file-type-badge.docx {
  background-color: var(--primary-color);
}

.file-type-badge.txt {
  background-color: var(--secondary-color);
}

.upload-form {
  max-width: 600px;
  margin: 0 auto;
}

.file-input-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  border: 2px dashed var(--border-color);
  border-radius: 0.5rem;
  background-color: rgba(0, 0, 0, 0.02);
  transition: all 0.3s ease-in-out;
  cursor: pointer;
}

.file-input-wrapper:hover {
  border-color: var(--primary-color);
}

.file-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.file-input-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.file-input-text {
  font-size: 1.25rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.file-input-subtext {
  font-size: 0.875rem;
  color: #718096;
}

.selected-file {
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.selected-file-name {
  font-weight: 500;
  margin-right: 1rem;
  word-break: break-all;
}

.remove-file {
  color: var(--error-color);
  cursor: pointer;
}

.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80vh;
}

.login-card {
  width: 100%;
  max-width: 400px;
}

.github-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0.75rem;
  background-color: #24292e;
  color: white;
  font-weight: 600;
  border-radius: 0.25rem;
  transition: all 0.3s ease-in-out;
}

.github-button:hover {
  background-color: #1a1e21;
}

.github-icon {
  margin-right: 0.5rem;
}

/* Responsive styles */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    padding: 1rem;
  }
  
  .nav {
    margin-top: 1rem;
    width: 100%;
    justify-content: space-between;
  }
  
  .report-list {
    grid-template-columns: 1fr;
  }
  
  .container {
    padding: 1rem;
  }
}