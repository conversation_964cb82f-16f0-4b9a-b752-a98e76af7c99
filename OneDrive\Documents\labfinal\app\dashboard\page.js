'use client';

import { useState, useEffect, useMemo } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useToast } from '../../components/Toast';
import { ReportListSkeleton, SearchFilterSkeleton } from '../../components/LoadingSkeleton';

export default function Dashboard() {
  const [reports, setReports] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const router = useRouter();

  useEffect(() => {
    // Check if user is logged in
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    const email = localStorage.getItem('userEmail');
    
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }
    
    setUserEmail(email || 'User');
    fetchReports();
  }, [router]);

  const fetchReports = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/reports');
      
      if (!response.ok) {
        throw new Error('Failed to fetch reports');
      }
      
      const data = await response.json();
      setReports(data.reports || []);
    } catch (error) {
      console.error('Error fetching reports:', error);
      setError('Failed to load reports. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteReport = async (reportId) => {
    if (!confirm('Are you sure you want to delete this report?')) {
      return;
    }

    try {
      const response = await fetch('/api/reports/delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reportId }),
      });

      if (!response.ok) {
        throw new Error('Failed to delete report');
      }

      // Refresh the reports list
      fetchReports();
    } catch (error) {
      console.error('Error deleting report:', error);
      setError('Failed to delete report. Please try again later.');
    }
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const getFileTypeBadgeClass = (fileType) => {
    return `file-type-badge ${fileType}`;
  };

  if (isLoading) {
    return (
      <div className="container">
        <div className="card">
          <h1>Loading...</h1>
        </div>
      </div>
    );
  }

  const handleLogout = () => {
    // Clear user data from localStorage
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('userEmail');
    
    // Redirect to login page
    router.push('/login');
  };

  return (
    <div className="container">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
        <h1>My Lab Reports</h1>
        <div>
          <span style={{ marginRight: '1rem' }}>Welcome, {userEmail}</span>
          <button 
            onClick={handleLogout}
            style={{ backgroundColor: '#718096' }}
          >
            Logout
          </button>
        </div>
      </div>
      
      <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '2rem' }}>
        <Link href="/upload">
          <button>Upload New Report</button>
        </Link>
      </div>

      {error && <div className="error">{error}</div>}

      {reports.length === 0 ? (
        <div className="card">
          <p>You haven't uploaded any reports yet.</p>
          <Link href="/upload">
            <button style={{ marginTop: '1rem' }}>Upload Your First Report</button>
          </Link>
        </div>
      ) : (
        <div className="report-list">
          {reports.map((report) => (
            <div key={report._id} className="card report-card">
              <div className="report-card-header">
                <h3 style={{ wordBreak: 'break-word' }}>{report.filename}</h3>
                <span className={getFileTypeBadgeClass(report.fileType)}>
                  {report.fileType.toUpperCase()}
                </span>
              </div>
              
              <div className="report-card-body">
                <p>Uploaded on {formatDate(report.uploadedAt)}</p>
              </div>
              
              <div className="report-card-footer">
                <Link href={`/dashboard/${report._id}`}>
                  <button>View Details</button>
                </Link>
                <button 
                  onClick={() => handleDeleteReport(report._id)}
                  style={{ backgroundColor: 'var(--error-color)' }}
                >
                  Delete
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}