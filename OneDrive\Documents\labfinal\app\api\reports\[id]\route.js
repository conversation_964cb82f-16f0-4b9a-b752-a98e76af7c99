import { NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

// Mock data for reports
const mockReports = [
  {
    _id: '1',
    filename: 'Blood Test Report.pdf',
    fileType: 'pdf',
    fileUrl: '/uploads/blood-test-report.pdf',
    userEmail: '<EMAIL>',
    uploadedAt: new Date('2023-10-15').toISOString()
  },
  {
    _id: '2',
    filename: 'X-Ray Analysis.docx',
    fileType: 'docx',
    fileUrl: '/uploads/xray-analysis.docx',
    userEmail: '<EMAIL>',
    uploadedAt: new Date('2023-10-10').toISOString()
  },
  {
    _id: '3',
    filename: 'Medical Notes.txt',
    fileType: 'txt',
    fileUrl: '/uploads/medical-notes.txt',
    userEmail: '<EMAIL>',
    uploadedAt: new Date('2023-10-05').toISOString()
  }
];

export async function GET(req, { params }) {
  try {
    const { id } = params;
    if (!id) {
      return NextResponse.json({ error: 'Report ID is required' }, { status: 400 });
    }

    // Find the report in mock data
    const report = mockReports.find(report => report._id === id);
    if (!report) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    return NextResponse.json({ report }, { status: 200 });
  } catch (error) {
    console.error('Error fetching report:', error);
    return NextResponse.json(
      { error: 'Error fetching report', details: error.message },
      { status: 500 }
    );
  }
}