'use client';

export function ReportCardSkeleton() {
  return (
    <div className="card report-card">
      <div className="report-card-header">
        <div className="skeleton skeleton-title" style={{ width: '70%' }}></div>
        <div className="skeleton" style={{ width: '60px', height: '24px', borderRadius: '12px' }}></div>
      </div>
      
      <div className="report-card-body">
        <div className="skeleton skeleton-text" style={{ width: '50%' }}></div>
      </div>
      
      <div className="report-card-footer">
        <div className="skeleton" style={{ width: '80px', height: '36px', borderRadius: '0.25rem' }}></div>
        <div className="skeleton" style={{ width: '60px', height: '36px', borderRadius: '0.25rem' }}></div>
      </div>
    </div>
  );
}

export function ReportListSkeleton({ count = 6 }) {
  return (
    <div className="report-list">
      {Array.from({ length: count }, (_, index) => (
        <ReportCardSkeleton key={index} />
      ))}
    </div>
  );
}

export function ReportDetailSkeleton() {
  return (
    <div className="container">
      <div style={{ marginBottom: '1rem' }}>
        <div className="skeleton" style={{ width: '150px', height: '36px', borderRadius: '0.25rem' }}></div>
      </div>

      <div className="card">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <div className="skeleton skeleton-title" style={{ width: '60%' }}></div>
          <div className="skeleton" style={{ width: '60px', height: '24px', borderRadius: '12px' }}></div>
        </div>

        <div style={{ margin: '2rem 0', padding: '2rem', backgroundColor: 'rgba(0,0,0,0.03)', borderRadius: '0.5rem', textAlign: 'center' }}>
          <div className="skeleton" style={{ width: '80px', height: '80px', borderRadius: '50%', margin: '0 auto 1rem' }}></div>
          <div className="skeleton skeleton-text" style={{ width: '200px', margin: '0 auto 0.5rem' }}></div>
          <div className="skeleton skeleton-text" style={{ width: '150px', margin: '0 auto' }}></div>
        </div>

        <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '2rem' }}>
          <div className="skeleton" style={{ width: '100px', height: '36px', borderRadius: '0.25rem' }}></div>
          <div className="skeleton" style={{ width: '120px', height: '36px', borderRadius: '0.25rem' }}></div>
        </div>
      </div>
    </div>
  );
}

export function SearchFilterSkeleton() {
  return (
    <div className="search-filter-container">
      <div className="search-input">
        <div className="skeleton" style={{ height: '42px', borderRadius: '0.25rem' }}></div>
      </div>
      <div className="filter-select">
        <div className="skeleton" style={{ height: '42px', borderRadius: '0.25rem' }}></div>
      </div>
      <div className="skeleton" style={{ width: '100px', height: '42px', borderRadius: '0.25rem' }}></div>
    </div>
  );
}
