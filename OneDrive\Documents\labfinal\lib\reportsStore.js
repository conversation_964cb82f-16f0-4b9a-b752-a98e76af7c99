// This is a simple in-memory store for lab reports.
// In a real-world application, you would use a database like MongoDB or PostgreSQL.

let reports = [
  {
    _id: '1',
    filename: 'Blood Test Report.pdf',
    fileType: 'pdf',
    fileUrl: '/uploads/blood-test-report.pdf',
    userEmail: '<EMAIL>',
    uploadedAt: new Date('2023-10-15').toISOString(),
  },
  {
    _id: '2',
    filename: 'X-Ray Analysis.docx',
    fileType: 'docx',
    fileUrl: '/uploads/xray-analysis.docx',
    userEmail: '<EMAIL>',
    uploadedAt: new Date('2023-10-10').toISOString(),
  },
  {
    _id: '3',
    filename: 'Medical Notes.txt',
    fileType: 'txt',
    fileUrl: '/uploads/medical-notes.txt',
    userEmail: '<EMAIL>',
    uploadedAt: new Date('2023-10-05').toISOString(),
  },
];

export const getReports = () => {
  return reports.sort((a, b) => new Date(b.uploadedAt) - new Date(a.uploadedAt));
};

export const addReport = (report) => {
  reports.push(report);
};

export const deleteReport = (reportId) => {
  reports = reports.filter((report) => report._id !== reportId);
};

export const getReportById = (reportId) => {
  return reports.find((report) => report._id === reportId);
};